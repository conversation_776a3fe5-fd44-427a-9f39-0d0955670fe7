import React from 'react';
import './App.css';

function App() {
  return (
    <div className="smart-property-deals">
      <header className="header">
        <div className="logo">Smart Property Deals</div>
        <nav className="nav">
          <a href="#buy">Buy</a>
          <a href="#rent">Rent</a>
          <a href="#sell">Sell</a>
          <a href="#loans">Home Loans</a>
          <a href="#agents">Property Agents</a>
        </nav>
        <div className="auth-buttons">
          <button className="login-btn">Login</button>
          <button className="signup-btn">Sign Up</button>
        </div>
      </header>

      <section className="hero">
        <h1>Find Your Dream Property</h1>
        <div className="search-container">
          <div className="search-tabs">
            <button className="active">Buy</button>
            <button>Rent</button>
            <button>PG/Co-living</button>
            <button>Commercial</button>
          </div>
          <div className="search-box">
            <input type="text" placeholder="Search location, project or landmark..." />
            <button className="search-btn">Search</button>
          </div>
        </div>
      </section>

      <section className="featured-properties">
        <h2>Featured Properties</h2>
        <div className="property-cards">
          {[1, 2, 3].map(item => (
            <div className="property-card" key={item}>
              <div className="property-img"></div>
              <div className="property-details">
                <h3>₹ 85 Lac</h3>
                <p>3 BHK Apartment</p>
                <p>Sector 123, Noida</p>
                <div className="property-features">
                  <span>1500 sq.ft</span>
                  <span>Semi-furnished</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className="services">
        <h2>Our Services</h2>
        <div className="service-cards">
          <div className="service-card">
            <div className="service-icon">🏠</div>
            <h3>Buy a Home</h3>
            <p>Find your place with an immersive photo experience and the most listings.</p>
          </div>
          <div className="service-card">
            <div className="service-icon">🔑</div>
            <h3>Rent a Home</h3>
            <p>We're creating a seamless online experience from shopping to renting.</p>
          </div>
          <div className="service-card">
            <div className="service-icon">💰</div>
            <h3>Home Loans</h3>
            <p>Get pre-approved by a local lender to increase your chances of making a successful offer.</p>
          </div>
        </div>
      </section>

      <footer className="footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>Smart Property Deals</h3>
            <p>Your trusted partner in real estate</p>
            <p>© 2023 Smart Property Deals. All rights reserved.</p>
          </div>
          <div className="footer-section">
            <h3>Quick Links</h3>
            <ul>
              <li><a href="#about">About Us</a></li>
              <li><a href="#contact">Contact Us</a></li>
              <li><a href="#careers">Careers</a></li>
              <li><a href="#terms">Terms & Conditions</a></li>
            </ul>
          </div>
          <div className="footer-section">
            <h3>Contact Info</h3>
            <p>Email: <EMAIL></p>
            <p>Phone: +91 9876543210</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;

