import React, { useState } from 'react';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('Buy');
  const [selectedCity, setSelectedCity] = useState('Mumbai');
  const [searchQuery, setSearchQuery] = useState('');

  const cities = ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Chennai', 'Hyderabad', 'Kolkata', 'Ahmedabad'];
  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];

  return (
    <div className="smart-property-deals">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <div className="logo-section">
            <div className="logo">Smart Property Deals</div>
            <div className="city-selector">
              <select value={selectedCity} onChange={(e) => setSelectedCity(e.target.value)}>
                {cities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
          </div>

          <nav className="main-nav">
            <div className="nav-item dropdown">
              <span>Buy</span>
              <div className="dropdown-content">
                <a href="#ready-to-move">Ready to Move</a>
                <a href="#owner-properties">Owner Properties</a>
                <a href="#budget-homes">Budget Homes</a>
                <a href="#premium-homes">Premium Homes</a>
                <a href="#new-projects">New Projects</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Rent</span>
              <div className="dropdown-content">
                <a href="#owner-properties">Owner Properties</a>
                <a href="#verified-properties">Verified Properties</a>
                <a href="#furnished-homes">Furnished Homes</a>
                <a href="#bachelor-friendly">Bachelor Friendly</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Sell</span>
              <div className="dropdown-content">
                <a href="#post-property">Post Property FREE</a>
                <a href="#my-dashboard">My Dashboard</a>
                <a href="#ad-packages">Ad Packages</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Home Loans</span>
              <div className="dropdown-content">
                <a href="#home-loans">Home Loans</a>
                <a href="#balance-transfer">Balance Transfer</a>
                <a href="#loan-against-property">Loan Against Property</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>SPD Advice</span>
              <div className="dropdown-content">
                <a href="#research-insights">Research & Insights</a>
                <a href="#property-valuation">Property Valuation</a>
                <a href="#rates-trends">Rates & Trends</a>
                <a href="#area-converter">Area Converter</a>
              </div>
            </div>
            <div className="nav-item">
              <span>Help</span>
            </div>
          </nav>

          <div className="header-actions">
            <button className="spd-prime-btn">SPD Prime</button>
            <button className="login-btn">Login</button>
            <button className="post-property-btn">Post Property FREE</button>
          </div>
        </div>
      </header>

      {/* Hero Search Section */}
      <section className="hero-search">
        <div className="search-container">
          <div className="search-tabs">
            {searchTabs.map(tab => (
              <button
                key={tab}
                className={`search-tab ${activeTab === tab ? 'active' : ''}`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
            <button className="post-ad-tab">Post Free Property Ad</button>
          </div>

          <div className="search-form">
            <div className="search-input-container">
              <input
                type="text"
                placeholder="Please enter a valid Location or Project"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="location-search"
              />
              <div className="search-suggestions">
                <small>You can enter: City, Locality, Area (like South Delhi), Project or Builder name</small>
              </div>
            </div>

            <div className="property-filters">
              <div className="property-type-section">
                <h4>Property Type</h4>
                <div className="property-types">
                  <div className="type-category">
                    <span className="category-title">Residential</span>
                    <div className="type-options">
                      <label><input type="checkbox" /> Flat</label>
                      <label><input type="checkbox" /> House/Villa</label>
                      <label><input type="checkbox" /> Plot</label>
                    </div>
                  </div>
                  <div className="bhk-options">
                    <label><input type="checkbox" /> 1 Bhk</label>
                    <label><input type="checkbox" /> 2 Bhk</label>
                    <label><input type="checkbox" /> 3 Bhk</label>
                    <label><input type="checkbox" /> 4 Bhk</label>
                  </div>
                </div>
              </div>

              <button className="search-btn">Search</button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="featured-properties">
        <div className="container">
          <h2>Featured Properties</h2>
          <div className="property-grid">
            {[1, 2, 3, 4, 5, 6].map(item => (
              <div className="property-card" key={item}>
                <div className="property-image">
                  <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80" alt="Property" />
                  <div className="property-badge">Ready to Move</div>
                </div>
                <div className="property-info">
                  <div className="price">₹ {85 + item * 10} Lac</div>
                  <div className="property-title">3 BHK Apartment</div>
                  <div className="property-location">Sector {120 + item}, Noida</div>
                  <div className="property-details">
                    <span>1500 sq.ft</span>
                    <span>Semi-furnished</span>
                  </div>
                  <div className="property-actions">
                    <button className="contact-btn">Contact Owner</button>
                    <button className="view-btn">View Details</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Projects */}
      <section className="popular-projects">
        <div className="container">
          <h2>Popular Projects in {selectedCity}</h2>
          <div className="projects-grid">
            {[
              { name: 'Lodha Palava', location: 'Dombivli East', price: '₹ 45 Lac onwards', developer: 'Lodha Group' },
              { name: 'Godrej Woods', location: 'Sector 43', price: '₹ 1.2 Cr onwards', developer: 'Godrej Properties' },
              { name: 'Mahagun Mywoods', location: 'Noida Extension', price: '₹ 65 Lac onwards', developer: 'Mahagun Group' },
              { name: 'DLF Camellias', location: 'Golf Course Road', price: '₹ 8 Cr onwards', developer: 'DLF Limited' },
              { name: 'Prestige Bella Vista', location: 'Iyyappanthangal', price: '₹ 75 Lac onwards', developer: 'Prestige Group' },
              { name: 'Brigade Xanadu', location: 'Mogappair West', price: '₹ 55 Lac onwards', developer: 'Brigade Group' }
            ].map((project, index) => (
              <div className="project-card" key={index}>
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80" alt="Project" />
                </div>
                <div className="project-info">
                  <div className="project-name">{project.name}</div>
                  <div className="project-location">{project.location}</div>
                  <div className="project-price">{project.price}</div>
                  <div className="project-developer">by {project.developer}</div>
                  <button className="view-project-btn">View Project</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services">
        <div className="container">
          <h2>Our Services</h2>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">🏠</div>
              <h3>Buy a Home</h3>
              <p>Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">🔑</div>
              <h3>Rent a Home</h3>
              <p>We're creating a seamless online experience – from shopping on the largest rental network, to applying, to paying rent.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">💰</div>
              <h3>Home Loans</h3>
              <p>Get pre-approved by a local lender to increase your chances of making a successful offer on a home.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">📊</div>
              <h3>Property Valuation</h3>
              <p>Get accurate property valuations and market insights to make informed real estate decisions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Smart Property Deals</h3>
              <p>As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.</p>
              <div className="social-links">
                <a href="#facebook">📘</a>
                <a href="#twitter">🐦</a>
                <a href="#linkedin">💼</a>
                <a href="#youtube">📺</a>
                <a href="#instagram">📷</a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Properties in India</h4>
              <ul>
                <li><a href="#mumbai">Property in Mumbai</a></li>
                <li><a href="#delhi">Property in New Delhi</a></li>
                <li><a href="#bangalore">Property in Bangalore</a></li>
                <li><a href="#pune">Property in Pune</a></li>
                <li><a href="#chennai">Property in Chennai</a></li>
                <li><a href="#hyderabad">Property in Hyderabad</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>New Projects</h4>
              <ul>
                <li><a href="#mumbai-projects">New Projects in Mumbai</a></li>
                <li><a href="#delhi-projects">New Projects in Delhi</a></li>
                <li><a href="#bangalore-projects">New Projects in Bangalore</a></li>
                <li><a href="#pune-projects">New Projects in Pune</a></li>
                <li><a href="#chennai-projects">New Projects in Chennai</a></li>
                <li><a href="#hyderabad-projects">New Projects in Hyderabad</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Property Services</h4>
              <ul>
                <li><a href="#home-loans">Home Loan</a></li>
                <li><a href="#property-valuation">Property Valuation</a></li>
                <li><a href="#legal-services">Legal Services</a></li>
                <li><a href="#home-interiors">Home Interiors</a></li>
                <li><a href="#packers-movers">Packers & Movers</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <div className="footer-links">
              <a href="#terms">Terms & Conditions</a>
              <a href="#privacy">Privacy Policy</a>
              <a href="#sitemap">Sitemap</a>
              <a href="#careers">Careers</a>
              <a href="#help">Help Center</a>
            </div>
            <p className="copyright">© 2025 Smart Property Deals. All Rights Reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;

