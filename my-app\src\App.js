import React, { useState } from 'react';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('Buy');
  const [searchQuery, setSearchQuery] = useState('');

  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];

  // Mumbai-specific data
  const mumbaiLocalities = [
    'Andheri East', 'Andheri West', 'Bandra East', 'Bandra West', 'Borivali East', 'Borivali West',
    'Chembur', 'Dadar East', 'Dadar West', 'Ghatkopar East', 'Ghatkopar West', 'Goregaon East',
    'Goregaon West', 'Juhu', 'Kandivali East', 'Kandivali West', 'Khar West', 'Kurla East',
    'Kurla West', 'Lower Parel', 'Malad East', 'Malad West', 'Matunga East', 'Matunga West',
    'Mulund East', 'Mulund West', 'Powai', 'Santa Cruz East', 'Santa Cruz West', 'Thane West',
    'Versova', 'Vikhroli East', 'Vikhroli West', 'Vile Parle East', 'Vile Parle West', 'Worli'
  ];

  return (
    <div className="smart-property-deals">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <div className="logo-section">
            <div className="logo">Smart Property Deals</div>
            <div className="city-display">
              <span className="current-city">Mumbai</span>
              <small>Currently serving Mumbai only</small>
            </div>
          </div>

          <nav className="main-nav">
            <div className="nav-item dropdown">
              <span>Buy</span>
              <div className="dropdown-content">
                <a href="#ready-to-move">Ready to Move</a>
                <a href="#owner-properties">Owner Properties</a>
                <a href="#budget-homes">Budget Homes</a>
                <a href="#premium-homes">Premium Homes</a>
                <a href="#new-projects">New Projects</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Rent</span>
              <div className="dropdown-content">
                <a href="#owner-properties">Owner Properties</a>
                <a href="#verified-properties">Verified Properties</a>
                <a href="#furnished-homes">Furnished Homes</a>
                <a href="#bachelor-friendly">Bachelor Friendly</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Sell</span>
              <div className="dropdown-content">
                <a href="#post-property">Post Property FREE</a>
                <a href="#my-dashboard">My Dashboard</a>
                <a href="#ad-packages">Ad Packages</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>Home Loans</span>
              <div className="dropdown-content">
                <a href="#home-loans">Home Loans</a>
                <a href="#balance-transfer">Balance Transfer</a>
                <a href="#loan-against-property">Loan Against Property</a>
              </div>
            </div>
            <div className="nav-item dropdown">
              <span>SPD Advice</span>
              <div className="dropdown-content">
                <a href="#research-insights">Research & Insights</a>
                <a href="#property-valuation">Property Valuation</a>
                <a href="#rates-trends">Rates & Trends</a>
                <a href="#area-converter">Area Converter</a>
              </div>
            </div>
            <div className="nav-item">
              <span>Help</span>
            </div>
          </nav>

          <div className="header-actions">
            <button className="spd-prime-btn">SPD Prime</button>
            <button className="login-btn">Login</button>
            <button className="post-property-btn">Post Property FREE</button>
          </div>
        </div>
      </header>

      {/* Hero Banner */}
      <section className="hero-banner">
        <div className="hero-content">
          <div className="hero-text">
            <h1>Find Your Dream Home in Mumbai</h1>
            <p>Discover the best properties in India's financial capital. From luxury apartments in South Mumbai to affordable homes in the suburbs.</p>
            <div className="hero-stats">
              <div className="stat">
                <span className="stat-number">50,000+</span>
                <span className="stat-label">Properties</span>
              </div>
              <div className="stat">
                <span className="stat-number">2 Lakh+</span>
                <span className="stat-label">Happy Customers</span>
              </div>
              <div className="stat">
                <span className="stat-number">500+</span>
                <span className="stat-label">Verified Agents</span>
              </div>
            </div>
          </div>
          <div className="hero-image">
            <img src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.0.3&w=600&q=80" alt="Mumbai Skyline" />
          </div>
        </div>
      </section>

      {/* Hero Search Section */}
      <section className="hero-search">
        <div className="search-container">
          <div className="search-tabs">
            {searchTabs.map(tab => (
              <button
                key={tab}
                className={`search-tab ${activeTab === tab ? 'active' : ''}`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
            <button className="post-ad-tab">Post Free Property Ad</button>
          </div>

          <div className="search-form">
            <div className="search-input-container">
              <input
                type="text"
                placeholder="Search in Mumbai - Enter locality, area or project name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="location-search"
              />
              <div className="search-suggestions">
                <small>You can enter: Locality (like Andheri, Bandra), Area (like Western Suburbs), Project or Builder name</small>
              </div>
            </div>

            <div className="property-filters">
              <div className="property-type-section">
                <h4>Property Type</h4>
                <div className="property-types">
                  <div className="type-category">
                    <span className="category-title">Residential</span>
                    <div className="type-options">
                      <label><input type="checkbox" /> Flat</label>
                      <label><input type="checkbox" /> House/Villa</label>
                      <label><input type="checkbox" /> Plot</label>
                    </div>
                  </div>
                  <div className="bhk-options">
                    <label><input type="checkbox" /> 1 Bhk</label>
                    <label><input type="checkbox" /> 2 Bhk</label>
                    <label><input type="checkbox" /> 3 Bhk</label>
                    <label><input type="checkbox" /> 4 Bhk</label>
                  </div>
                </div>
              </div>

              <button className="search-btn">Search</button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="featured-properties">
        <div className="container">
          <h2>Featured Properties in Mumbai</h2>
          <div className="property-grid">
            {[
              { price: '₹ 1.2 Cr', type: '2 BHK Apartment', location: 'Andheri West', area: '850 sq.ft', furnishing: 'Semi-furnished' },
              { price: '₹ 2.5 Cr', type: '3 BHK Apartment', location: 'Bandra West', area: '1200 sq.ft', furnishing: 'Fully-furnished' },
              { price: '₹ 95 Lac', type: '2 BHK Apartment', location: 'Malad West', area: '750 sq.ft', furnishing: 'Unfurnished' },
              { price: '₹ 1.8 Cr', type: '3 BHK Apartment', location: 'Powai', area: '1100 sq.ft', furnishing: 'Semi-furnished' },
              { price: '₹ 3.2 Cr', type: '4 BHK Apartment', location: 'Juhu', area: '1800 sq.ft', furnishing: 'Fully-furnished' },
              { price: '₹ 1.5 Cr', type: '3 BHK Apartment', location: 'Goregaon West', area: '1000 sq.ft', furnishing: 'Semi-furnished' }
            ].map((property, index) => (
              <div className="property-card" key={index}>
                <div className="property-image">
                  <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80" alt="Property" />
                  <div className="property-badge">Ready to Move</div>
                </div>
                <div className="property-info">
                  <div className="price">{property.price}</div>
                  <div className="property-title">{property.type}</div>
                  <div className="property-location">{property.location}, Mumbai</div>
                  <div className="property-details">
                    <span>{property.area}</span>
                    <span>{property.furnishing}</span>
                  </div>
                  <div className="property-actions">
                    <button className="contact-btn">Contact Owner</button>
                    <button className="view-btn">View Details</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Projects */}
      <section className="popular-projects">
        <div className="container">
          <h2>Popular Projects in Mumbai</h2>
          <div className="projects-grid">
            {[
              { name: 'Lodha Park', location: 'Lower Parel', price: '₹ 3.5 Cr onwards', developer: 'Lodha Group' },
              { name: 'Godrej Platinum', location: 'Vikhroli East', price: '₹ 1.8 Cr onwards', developer: 'Godrej Properties' },
              { name: 'Oberoi Realty', location: 'Goregaon East', price: '₹ 2.2 Cr onwards', developer: 'Oberoi Realty' },
              { name: 'Hiranandani Gardens', location: 'Powai', price: '₹ 2.8 Cr onwards', developer: 'Hiranandani Group' },
              { name: 'Runwal Forests', location: 'Kanjurmarg West', price: '₹ 1.5 Cr onwards', developer: 'Runwal Group' },
              { name: 'Kalpataru Immensa', location: 'Thane West', price: '₹ 1.2 Cr onwards', developer: 'Kalpataru Group' }
            ].map((project, index) => (
              <div className="project-card" key={index}>
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80" alt="Project" />
                </div>
                <div className="project-info">
                  <div className="project-name">{project.name}</div>
                  <div className="project-location">{project.location}, Mumbai</div>
                  <div className="project-price">{project.price}</div>
                  <div className="project-developer">by {project.developer}</div>
                  <button className="view-project-btn">View Project</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mumbai Highlights */}
      <section className="mumbai-highlights">
        <div className="container">
          <h2>Why Choose Mumbai?</h2>
          <div className="highlights-grid">
            <div className="highlight-card">
              <div className="highlight-icon">🏙️</div>
              <h3>Financial Capital</h3>
              <p>Home to India's major financial institutions, stock exchanges, and corporate headquarters.</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">🚇</div>
              <h3>Excellent Connectivity</h3>
              <p>Well-connected metro, local trains, and upcoming infrastructure projects.</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">🎭</div>
              <h3>Cultural Hub</h3>
              <p>Bollywood, art galleries, theaters, and diverse cultural experiences.</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">🏖️</div>
              <h3>Coastal Living</h3>
              <p>Beautiful beaches, sea-facing properties, and pleasant coastal climate.</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">🍽️</div>
              <h3>Food Paradise</h3>
              <p>Street food, fine dining, and culinary diversity from across India.</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">💼</div>
              <h3>Career Opportunities</h3>
              <p>Abundant job opportunities across industries and sectors.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services">
        <div className="container">
          <h2>Our Services</h2>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">🏠</div>
              <h3>Buy a Home</h3>
              <p>Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">🔑</div>
              <h3>Rent a Home</h3>
              <p>We're creating a seamless online experience – from shopping on the largest rental network, to applying, to paying rent.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">💰</div>
              <h3>Home Loans</h3>
              <p>Get pre-approved by a local lender to increase your chances of making a successful offer on a home.</p>
            </div>
            <div className="service-card">
              <div className="service-icon">📊</div>
              <h3>Property Valuation</h3>
              <p>Get accurate property valuations and market insights to make informed real estate decisions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Smart Property Deals</h3>
              <p>As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.</p>
              <div className="social-links">
                <a href="#facebook">📘</a>
                <a href="#twitter">🐦</a>
                <a href="#linkedin">💼</a>
                <a href="#youtube">📺</a>
                <a href="#instagram">📷</a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Properties in Mumbai</h4>
              <ul>
                <li><a href="#andheri">Property in Andheri</a></li>
                <li><a href="#bandra">Property in Bandra</a></li>
                <li><a href="#powai">Property in Powai</a></li>
                <li><a href="#juhu">Property in Juhu</a></li>
                <li><a href="#malad">Property in Malad</a></li>
                <li><a href="#goregaon">Property in Goregaon</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Mumbai Areas</h4>
              <ul>
                <li><a href="#western-suburbs">Western Suburbs</a></li>
                <li><a href="#central-mumbai">Central Mumbai</a></li>
                <li><a href="#south-mumbai">South Mumbai</a></li>
                <li><a href="#eastern-suburbs">Eastern Suburbs</a></li>
                <li><a href="#thane">Thane</a></li>
                <li><a href="#navi-mumbai">Navi Mumbai</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Property Services</h4>
              <ul>
                <li><a href="#home-loans">Home Loan</a></li>
                <li><a href="#property-valuation">Property Valuation</a></li>
                <li><a href="#legal-services">Legal Services</a></li>
                <li><a href="#home-interiors">Home Interiors</a></li>
                <li><a href="#packers-movers">Packers & Movers</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <div className="footer-links">
              <a href="#terms">Terms & Conditions</a>
              <a href="#privacy">Privacy Policy</a>
              <a href="#sitemap">Sitemap</a>
              <a href="#careers">Careers</a>
              <a href="#help">Help Center</a>
            </div>
            <p className="copyright">© 2025 Smart Property Deals. All Rights Reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;

