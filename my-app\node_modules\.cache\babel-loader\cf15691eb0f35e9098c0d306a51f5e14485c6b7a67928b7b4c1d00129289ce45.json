{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\REALEASTATE\\\\my-app\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [activeTab, setActiveTab] = useState('Buy');\n  const [searchQuery, setSearchQuery] = useState('');\n  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];\n\n  // Mumbai-specific data\n  const mumbaiLocalities = ['Andheri East', 'Andheri West', 'Bandra East', 'Bandra West', 'Borivali East', 'Borivali West', 'Chembur', 'Dadar East', 'Dadar West', 'Ghatkopar East', 'Ghatkopar West', 'Goregaon East', 'Goregaon West', 'Juhu', 'Kandivali East', 'Kandivali West', 'Khar West', 'Kurla East', 'Kurla West', 'Lower Parel', 'Malad East', 'Malad West', 'Matunga East', 'Matunga West', 'Mulund East', 'Mulund West', 'Powai', 'Santa Cruz East', 'Santa Cruz West', 'Thane West', 'Versova', 'Vikhroli East', 'Vikhroli West', 'Vile Parle East', 'Vile Parle West', 'Worli'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"smart-property-deals\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: \"Smart Property Deals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"city-display\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-city\",\n              children: \"Mumbai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Currently serving Mumbai only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"main-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Buy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#ready-to-move\",\n                children: \"Ready to Move\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#owner-properties\",\n                children: \"Owner Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#budget-homes\",\n                children: \"Budget Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#premium-homes\",\n                children: \"Premium Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#new-projects\",\n                children: \"New Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Rent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#owner-properties\",\n                children: \"Owner Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#verified-properties\",\n                children: \"Verified Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#furnished-homes\",\n                children: \"Furnished Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#bachelor-friendly\",\n                children: \"Bachelor Friendly\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Sell\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#post-property\",\n                children: \"Post Property FREE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#my-dashboard\",\n                children: \"My Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#ad-packages\",\n                children: \"Ad Packages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Home Loans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#home-loans\",\n                children: \"Home Loans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#balance-transfer\",\n                children: \"Balance Transfer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#loan-against-property\",\n                children: \"Loan Against Property\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"SPD Advice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#research-insights\",\n                children: \"Research & Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#property-valuation\",\n                children: \"Property Valuation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#rates-trends\",\n                children: \"Rates & Trends\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#area-converter\",\n                children: \"Area Converter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"spd-prime-btn\",\n            children: \"SPD Prime\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"login-btn\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"post-property-btn\",\n            children: \"Post Property FREE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-search\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-tabs\",\n          children: [searchTabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `search-tab ${activeTab === tab ? 'active' : ''}`,\n            onClick: () => setActiveTab(tab),\n            children: tab\n          }, tab, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"post-ad-tab\",\n            children: \"Post Free Property Ad\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Please enter a valid Location or Project\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"location-search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-suggestions\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"You can enter: City, Locality, Area (like South Delhi), Project or Builder name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-type-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Property Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-types\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"type-category\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"category-title\",\n                    children: \"Residential\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"type-options\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 128,\n                        columnNumber: 30\n                      }, this), \" Flat\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 129,\n                        columnNumber: 30\n                      }, this), \" House/Villa\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 130,\n                        columnNumber: 30\n                      }, this), \" Plot\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 130,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bhk-options\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 28\n                    }, this), \" 1 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 28\n                    }, this), \" 2 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 28\n                    }, this), \" 3 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 28\n                    }, this), \" 4 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-btn\",\n              children: \"Search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-properties\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Featured Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"property-grid\",\n          children: [1, 2, 3, 4, 5, 6].map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-image\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80\",\n                alt: \"Property\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-badge\",\n                children: \"Ready to Move\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price\",\n                children: [\"\\u20B9 \", 85 + item * 10, \" Lac\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-title\",\n                children: \"3 BHK Apartment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-location\",\n                children: [\"Sector \", 120 + item, \", Noida\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"1500 sq.ft\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Semi-furnished\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-btn\",\n                  children: \"Contact Owner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"view-btn\",\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, item, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"popular-projects\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Popular Projects in \", selectedCity]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"projects-grid\",\n          children: [{\n            name: 'Lodha Palava',\n            location: 'Dombivli East',\n            price: '₹ 45 Lac onwards',\n            developer: 'Lodha Group'\n          }, {\n            name: 'Godrej Woods',\n            location: 'Sector 43',\n            price: '₹ 1.2 Cr onwards',\n            developer: 'Godrej Properties'\n          }, {\n            name: 'Mahagun Mywoods',\n            location: 'Noida Extension',\n            price: '₹ 65 Lac onwards',\n            developer: 'Mahagun Group'\n          }, {\n            name: 'DLF Camellias',\n            location: 'Golf Course Road',\n            price: '₹ 8 Cr onwards',\n            developer: 'DLF Limited'\n          }, {\n            name: 'Prestige Bella Vista',\n            location: 'Iyyappanthangal',\n            price: '₹ 75 Lac onwards',\n            developer: 'Prestige Group'\n          }, {\n            name: 'Brigade Xanadu',\n            location: 'Mogappair West',\n            price: '₹ 55 Lac onwards',\n            developer: 'Brigade Group'\n          }].map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80\",\n                alt: \"Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-name\",\n                children: project.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-location\",\n                children: project.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-price\",\n                children: project.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-developer\",\n                children: [\"by \", project.developer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"view-project-btn\",\n                children: \"View Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"services\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Our Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83C\\uDFE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Buy a Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDD11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Rent a Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We're creating a seamless online experience \\u2013 from shopping on the largest rental network, to applying, to paying rent.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Home Loans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get pre-approved by a local lender to increase your chances of making a successful offer on a home.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Property Valuation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get accurate property valuations and market insights to make informed real estate decisions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Smart Property Deals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-links\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#facebook\",\n                children: \"\\uD83D\\uDCD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#twitter\",\n                children: \"\\uD83D\\uDC26\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#linkedin\",\n                children: \"\\uD83D\\uDCBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#youtube\",\n                children: \"\\uD83D\\uDCFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#instagram\",\n                children: \"\\uD83D\\uDCF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Properties in India\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#mumbai\",\n                  children: \"Property in Mumbai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#delhi\",\n                  children: \"Property in New Delhi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#bangalore\",\n                  children: \"Property in Bangalore\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#pune\",\n                  children: \"Property in Pune\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#chennai\",\n                  children: \"Property in Chennai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#hyderabad\",\n                  children: \"Property in Hyderabad\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"New Projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#mumbai-projects\",\n                  children: \"New Projects in Mumbai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#delhi-projects\",\n                  children: \"New Projects in Delhi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#bangalore-projects\",\n                  children: \"New Projects in Bangalore\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#pune-projects\",\n                  children: \"New Projects in Pune\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#chennai-projects\",\n                  children: \"New Projects in Chennai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#hyderabad-projects\",\n                  children: \"New Projects in Hyderabad\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Property Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#home-loans\",\n                  children: \"Home Loan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#property-valuation\",\n                  children: \"Property Valuation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#legal-services\",\n                  children: \"Legal Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#home-interiors\",\n                  children: \"Home Interiors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#packers-movers\",\n                  children: \"Packers & Movers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#terms\",\n              children: \"Terms & Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#privacy\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#sitemap\",\n              children: \"Sitemap\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#careers\",\n              children: \"Careers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#help\",\n              children: \"Help Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"copyright\",\n            children: \"\\xA9 2025 Smart Property Deals. All Rights Reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"x6FcSKa4OQMdhdDXB8LaHG+t25E=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "App", "_s", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "searchTabs", "mumbaiLocalities", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "map", "tab", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "item", "src", "alt", "selectedCity", "name", "location", "price", "developer", "project", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/REALEASTATE/my-app/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [activeTab, setActiveTab] = useState('Buy');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n\r\n  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];\r\n\r\n  // Mumbai-specific data\r\n  const mumbaiLocalities = [\r\n    'Andheri East', 'Andheri West', 'Bandra East', 'Bandra West', 'Borivali East', 'Borivali West',\r\n    'Chembur', 'Dadar East', 'Dadar West', 'Ghatkopar East', 'Ghatkopar West', 'Goregaon East',\r\n    'Goregaon West', 'Juhu', 'Kandivali East', 'Kandivali West', 'Khar West', 'Kurla East',\r\n    'Kurla West', 'Lower Parel', 'Malad East', 'Malad West', 'Matunga East', 'Matunga West',\r\n    'Mulund East', 'Mulund West', 'Powai', 'Santa Cruz East', 'Santa Cruz West', 'Thane West',\r\n    'Versova', 'Vikhroli East', 'Vikhroli West', 'Vile Parle East', 'Vile Parle West', 'Worli'\r\n  ];\r\n\r\n  return (\r\n    <div className=\"smart-property-deals\">\r\n      {/* Header */}\r\n      <header className=\"header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"logo-section\">\r\n            <div className=\"logo\">Smart Property Deals</div>\r\n            <div className=\"city-display\">\r\n              <span className=\"current-city\">Mumbai</span>\r\n              <small>Currently serving Mumbai only</small>\r\n            </div>\r\n          </div>\r\n\r\n          <nav className=\"main-nav\">\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Buy</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#ready-to-move\">Ready to Move</a>\r\n                <a href=\"#owner-properties\">Owner Properties</a>\r\n                <a href=\"#budget-homes\">Budget Homes</a>\r\n                <a href=\"#premium-homes\">Premium Homes</a>\r\n                <a href=\"#new-projects\">New Projects</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Rent</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#owner-properties\">Owner Properties</a>\r\n                <a href=\"#verified-properties\">Verified Properties</a>\r\n                <a href=\"#furnished-homes\">Furnished Homes</a>\r\n                <a href=\"#bachelor-friendly\">Bachelor Friendly</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Sell</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#post-property\">Post Property FREE</a>\r\n                <a href=\"#my-dashboard\">My Dashboard</a>\r\n                <a href=\"#ad-packages\">Ad Packages</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Home Loans</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#home-loans\">Home Loans</a>\r\n                <a href=\"#balance-transfer\">Balance Transfer</a>\r\n                <a href=\"#loan-against-property\">Loan Against Property</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>SPD Advice</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#research-insights\">Research & Insights</a>\r\n                <a href=\"#property-valuation\">Property Valuation</a>\r\n                <a href=\"#rates-trends\">Rates & Trends</a>\r\n                <a href=\"#area-converter\">Area Converter</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item\">\r\n              <span>Help</span>\r\n            </div>\r\n          </nav>\r\n\r\n          <div className=\"header-actions\">\r\n            <button className=\"spd-prime-btn\">SPD Prime</button>\r\n            <button className=\"login-btn\">Login</button>\r\n            <button className=\"post-property-btn\">Post Property FREE</button>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Hero Search Section */}\r\n      <section className=\"hero-search\">\r\n        <div className=\"search-container\">\r\n          <div className=\"search-tabs\">\r\n            {searchTabs.map(tab => (\r\n              <button\r\n                key={tab}\r\n                className={`search-tab ${activeTab === tab ? 'active' : ''}`}\r\n                onClick={() => setActiveTab(tab)}\r\n              >\r\n                {tab}\r\n              </button>\r\n            ))}\r\n            <button className=\"post-ad-tab\">Post Free Property Ad</button>\r\n          </div>\r\n\r\n          <div className=\"search-form\">\r\n            <div className=\"search-input-container\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Please enter a valid Location or Project\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n                className=\"location-search\"\r\n              />\r\n              <div className=\"search-suggestions\">\r\n                <small>You can enter: City, Locality, Area (like South Delhi), Project or Builder name</small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"property-filters\">\r\n              <div className=\"property-type-section\">\r\n                <h4>Property Type</h4>\r\n                <div className=\"property-types\">\r\n                  <div className=\"type-category\">\r\n                    <span className=\"category-title\">Residential</span>\r\n                    <div className=\"type-options\">\r\n                      <label><input type=\"checkbox\" /> Flat</label>\r\n                      <label><input type=\"checkbox\" /> House/Villa</label>\r\n                      <label><input type=\"checkbox\" /> Plot</label>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"bhk-options\">\r\n                    <label><input type=\"checkbox\" /> 1 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 2 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 3 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 4 Bhk</label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <button className=\"search-btn\">Search</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Featured Properties */}\r\n      <section className=\"featured-properties\">\r\n        <div className=\"container\">\r\n          <h2>Featured Properties</h2>\r\n          <div className=\"property-grid\">\r\n            {[1, 2, 3, 4, 5, 6].map(item => (\r\n              <div className=\"property-card\" key={item}>\r\n                <div className=\"property-image\">\r\n                  <img src=\"https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80\" alt=\"Property\" />\r\n                  <div className=\"property-badge\">Ready to Move</div>\r\n                </div>\r\n                <div className=\"property-info\">\r\n                  <div className=\"price\">₹ {85 + item * 10} Lac</div>\r\n                  <div className=\"property-title\">3 BHK Apartment</div>\r\n                  <div className=\"property-location\">Sector {120 + item}, Noida</div>\r\n                  <div className=\"property-details\">\r\n                    <span>1500 sq.ft</span>\r\n                    <span>Semi-furnished</span>\r\n                  </div>\r\n                  <div className=\"property-actions\">\r\n                    <button className=\"contact-btn\">Contact Owner</button>\r\n                    <button className=\"view-btn\">View Details</button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Popular Projects */}\r\n      <section className=\"popular-projects\">\r\n        <div className=\"container\">\r\n          <h2>Popular Projects in {selectedCity}</h2>\r\n          <div className=\"projects-grid\">\r\n            {[\r\n              { name: 'Lodha Palava', location: 'Dombivli East', price: '₹ 45 Lac onwards', developer: 'Lodha Group' },\r\n              { name: 'Godrej Woods', location: 'Sector 43', price: '₹ 1.2 Cr onwards', developer: 'Godrej Properties' },\r\n              { name: 'Mahagun Mywoods', location: 'Noida Extension', price: '₹ 65 Lac onwards', developer: 'Mahagun Group' },\r\n              { name: 'DLF Camellias', location: 'Golf Course Road', price: '₹ 8 Cr onwards', developer: 'DLF Limited' },\r\n              { name: 'Prestige Bella Vista', location: 'Iyyappanthangal', price: '₹ 75 Lac onwards', developer: 'Prestige Group' },\r\n              { name: 'Brigade Xanadu', location: 'Mogappair West', price: '₹ 55 Lac onwards', developer: 'Brigade Group' }\r\n            ].map((project, index) => (\r\n              <div className=\"project-card\" key={index}>\r\n                <div className=\"project-image\">\r\n                  <img src=\"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80\" alt=\"Project\" />\r\n                </div>\r\n                <div className=\"project-info\">\r\n                  <div className=\"project-name\">{project.name}</div>\r\n                  <div className=\"project-location\">{project.location}</div>\r\n                  <div className=\"project-price\">{project.price}</div>\r\n                  <div className=\"project-developer\">by {project.developer}</div>\r\n                  <button className=\"view-project-btn\">View Project</button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Services Section */}\r\n      <section className=\"services\">\r\n        <div className=\"container\">\r\n          <h2>Our Services</h2>\r\n          <div className=\"services-grid\">\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">🏠</div>\r\n              <h3>Buy a Home</h3>\r\n              <p>Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">🔑</div>\r\n              <h3>Rent a Home</h3>\r\n              <p>We're creating a seamless online experience – from shopping on the largest rental network, to applying, to paying rent.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">💰</div>\r\n              <h3>Home Loans</h3>\r\n              <p>Get pre-approved by a local lender to increase your chances of making a successful offer on a home.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">📊</div>\r\n              <h3>Property Valuation</h3>\r\n              <p>Get accurate property valuations and market insights to make informed real estate decisions.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"container\">\r\n          <div className=\"footer-content\">\r\n            <div className=\"footer-section\">\r\n              <h3>Smart Property Deals</h3>\r\n              <p>As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.</p>\r\n              <div className=\"social-links\">\r\n                <a href=\"#facebook\">📘</a>\r\n                <a href=\"#twitter\">🐦</a>\r\n                <a href=\"#linkedin\">💼</a>\r\n                <a href=\"#youtube\">📺</a>\r\n                <a href=\"#instagram\">📷</a>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>Properties in India</h4>\r\n              <ul>\r\n                <li><a href=\"#mumbai\">Property in Mumbai</a></li>\r\n                <li><a href=\"#delhi\">Property in New Delhi</a></li>\r\n                <li><a href=\"#bangalore\">Property in Bangalore</a></li>\r\n                <li><a href=\"#pune\">Property in Pune</a></li>\r\n                <li><a href=\"#chennai\">Property in Chennai</a></li>\r\n                <li><a href=\"#hyderabad\">Property in Hyderabad</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>New Projects</h4>\r\n              <ul>\r\n                <li><a href=\"#mumbai-projects\">New Projects in Mumbai</a></li>\r\n                <li><a href=\"#delhi-projects\">New Projects in Delhi</a></li>\r\n                <li><a href=\"#bangalore-projects\">New Projects in Bangalore</a></li>\r\n                <li><a href=\"#pune-projects\">New Projects in Pune</a></li>\r\n                <li><a href=\"#chennai-projects\">New Projects in Chennai</a></li>\r\n                <li><a href=\"#hyderabad-projects\">New Projects in Hyderabad</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>Property Services</h4>\r\n              <ul>\r\n                <li><a href=\"#home-loans\">Home Loan</a></li>\r\n                <li><a href=\"#property-valuation\">Property Valuation</a></li>\r\n                <li><a href=\"#legal-services\">Legal Services</a></li>\r\n                <li><a href=\"#home-interiors\">Home Interiors</a></li>\r\n                <li><a href=\"#packers-movers\">Packers & Movers</a></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"footer-bottom\">\r\n            <div className=\"footer-links\">\r\n              <a href=\"#terms\">Terms & Conditions</a>\r\n              <a href=\"#privacy\">Privacy Policy</a>\r\n              <a href=\"#sitemap\">Sitemap</a>\r\n              <a href=\"#careers\">Careers</a>\r\n              <a href=\"#help\">Help Center</a>\r\n            </div>\r\n            <p className=\"copyright\">© 2025 Smart Property Deals. All Rights Reserved.</p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMS,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,CAAC;;EAE9E;EACA,MAAMC,gBAAgB,GAAG,CACvB,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAC9F,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,EAC1F,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EACtF,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EACvF,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EACzF,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,CAC3F;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBAEnCV,OAAA;MAAQS,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBV,OAAA;QAAKS,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BV,OAAA;UAAKS,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BV,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAMS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5Cd,OAAA;cAAAU,QAAA,EAAO;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBV,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxCd,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,sBAAsB;gBAAAL,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtDd,OAAA;gBAAGe,IAAI,EAAC,kBAAkB;gBAAAL,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9Cd,OAAA;gBAAGe,IAAI,EAAC,oBAAoB;gBAAAL,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/Cd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxCd,OAAA;gBAAGe,IAAI,EAAC,cAAc;gBAAAL,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,aAAa;gBAAAL,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpCd,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,wBAAwB;gBAAAL,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,oBAAoB;gBAAAL,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDd,OAAA;gBAAGe,IAAI,EAAC,qBAAqB;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,iBAAiB;gBAAAL,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BV,OAAA;YAAQS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDd,OAAA;YAAQS,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5Cd,OAAA;YAAQS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTd,OAAA;MAASS,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9BV,OAAA;QAAKS,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BV,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBH,UAAU,CAACS,GAAG,CAACC,GAAG,iBACjBjB,OAAA;YAEES,SAAS,EAAE,cAAcN,SAAS,KAAKc,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC7DC,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACa,GAAG,CAAE;YAAAP,QAAA,EAEhCO;UAAG,GAJCA,GAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKF,CACT,CAAC,eACFd,OAAA;YAAQS,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BV,OAAA;YAAKS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCV,OAAA;cACEmB,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,0CAA0C;cACtDC,KAAK,EAAEhB,WAAY;cACnBiB,QAAQ,EAAGC,CAAC,IAAKjB,cAAc,CAACiB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDZ,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFd,OAAA;cAAKS,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACjCV,OAAA;gBAAAU,QAAA,EAAO;cAA+E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BV,OAAA;cAAKS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCV,OAAA;gBAAAU,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BV,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BV,OAAA;oBAAMS,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDd,OAAA;oBAAKS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BV,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOmB,IAAI,EAAC;sBAAU;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAAK;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7Cd,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOmB,IAAI,EAAC;sBAAU;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAY;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpDd,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOmB,IAAI,EAAC;sBAAU;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAAK;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BV,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOmB,IAAI,EAAC;oBAAU;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOmB,IAAI,EAAC;oBAAU;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOmB,IAAI,EAAC;oBAAU;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOmB,IAAI,EAAC;oBAAU;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENd,OAAA;cAAQS,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5Bd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACM,GAAG,CAACS,IAAI,iBAC1BzB,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BV,OAAA;gBAAK0B,GAAG,EAAC,qFAAqF;gBAACC,GAAG,EAAC;cAAU;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChHd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BV,OAAA;gBAAKS,SAAS,EAAC,OAAO;gBAAAC,QAAA,GAAC,SAAE,EAAC,EAAE,GAAGe,IAAI,GAAG,EAAE,EAAC,MAAI;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDd,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,SAAO,EAAC,GAAG,GAAGe,IAAI,EAAC,SAAO;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnEd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BV,OAAA;kBAAAU,QAAA,EAAM;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvBd,OAAA;kBAAAU,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BV,OAAA;kBAAQS,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDd,OAAA;kBAAQS,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAjB4BW,IAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBnC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,GAAI,sBAAoB,EAACkB,YAAY;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3Cd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B,CACC;YAAEmB,IAAI,EAAE,cAAc;YAAEC,QAAQ,EAAE,eAAe;YAAEC,KAAK,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAc,CAAC,EACxG;YAAEH,IAAI,EAAE,cAAc;YAAEC,QAAQ,EAAE,WAAW;YAAEC,KAAK,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAoB,CAAC,EAC1G;YAAEH,IAAI,EAAE,iBAAiB;YAAEC,QAAQ,EAAE,iBAAiB;YAAEC,KAAK,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAgB,CAAC,EAC/G;YAAEH,IAAI,EAAE,eAAe;YAAEC,QAAQ,EAAE,kBAAkB;YAAEC,KAAK,EAAE,gBAAgB;YAAEC,SAAS,EAAE;UAAc,CAAC,EAC1G;YAAEH,IAAI,EAAE,sBAAsB;YAAEC,QAAQ,EAAE,iBAAiB;YAAEC,KAAK,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAiB,CAAC,EACrH;YAAEH,IAAI,EAAE,gBAAgB;YAAEC,QAAQ,EAAE,gBAAgB;YAAEC,KAAK,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAgB,CAAC,CAC9G,CAAChB,GAAG,CAAC,CAACiB,OAAO,EAAEC,KAAK,kBACnBlC,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BV,OAAA;gBAAK0B,GAAG,EAAC,qFAAqF;gBAACC,GAAG,EAAC;cAAS;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAKS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEuB,OAAO,CAACJ;cAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEuB,OAAO,CAACH;cAAQ;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1Dd,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEuB,OAAO,CAACF;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDd,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,KAAG,EAACuB,OAAO,CAACD,SAAS;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/Dd,OAAA;gBAAQS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA,GAV2BoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWnC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,UAAU;MAAAC,QAAA,eAC3BV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBd,OAAA;cAAAU,QAAA,EAAG;YAAwH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBd,OAAA;cAAAU,QAAA,EAAG;YAAuH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBd,OAAA;cAAAU,QAAA,EAAG;YAAmG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3Bd,OAAA;cAAAU,QAAA,EAAG;YAA4F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAAQS,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAKS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BV,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7Bd,OAAA;cAAAU,QAAA,EAAG;YAA+J;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtKd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAGe,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Bd,OAAA;gBAAGe,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzBd,OAAA;gBAAGe,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Bd,OAAA;gBAAGe,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzBd,OAAA;gBAAGe,IAAI,EAAC,YAAY;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5Bd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,QAAQ;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,YAAY;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,UAAU;kBAAAL,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,YAAY;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,kBAAkB;kBAAAL,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9Dd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5Dd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,gBAAgB;kBAAAL,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1Dd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,mBAAmB;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Bd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,aAAa;kBAAAL,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7Dd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAGe,IAAI,EAAC,QAAQ;cAAAL,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvCd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Bd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Bd,OAAA;cAAGe,IAAI,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNd,OAAA;YAAGS,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACZ,EAAA,CA3SQD,GAAG;AAAAkC,EAAA,GAAHlC,GAAG;AA6SZ,eAAeA,GAAG;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}