/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

a {
  text-decoration: none;
  color: inherit;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Smart Property Deals Styles */
.smart-property-deals {
  width: 100%;
}

/* Header - Housing.com Style */
.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
  height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 24px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #ff6600;
  text-decoration: none;
}

.city-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
}

.current-city {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.city-display small {
  color: #666;
  font-size: 12px;
  font-weight: 400;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 0;
}

.nav-item {
  position: relative;
  padding: 20px 16px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.nav-item:hover {
  color: #ff6600;
  border-bottom-color: #ff6600;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 8px 0;
  z-index: 1001;
  border: 1px solid #e6e6e6;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content a {
  display: block;
  padding: 10px 16px;
  color: #333;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.dropdown-content a:hover {
  background: #f8f9fa;
  color: #ff6600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.spd-prime-btn {
  background: #ff6600;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.spd-prime-btn:hover {
  background: #e55a00;
}

.login-btn {
  background: transparent;
  border: 1px solid #ff6600;
  color: #ff6600;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.login-btn:hover {
  background: #ff6600;
  color: white;
}

.post-property-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.post-property-btn:hover {
  background: #218838;
}

/* Hero Banner */
.hero-banner {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.hero-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-text h1 {
  font-size: 48px;
  font-weight: 800;
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hero-text p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  line-height: 1.6;
  font-weight: 500;
}

.hero-stats {
  display: flex;
  gap: 40px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 800;
  color: white;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.hero-image {
  position: relative;
}

.hero-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transform: perspective(1000px) rotateY(-5deg);
  transition: transform 0.3s ease;
}

.hero-image:hover img {
  transform: perspective(1000px) rotateY(0deg);
}

/* Hero Search Section */
.hero-search {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 0;
  position: relative;
  overflow: hidden;
}

.hero-search::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.search-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 6px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-tab {
  flex: 1;
  padding: 14px 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.search-tab.active {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.search-tab:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.post-ad-tab {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 12px;
  font-weight: 700;
  cursor: pointer;
  margin-left: 12px;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.post-ad-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.search-form {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-input-container {
  margin-bottom: 25px;
}

.location-search {
  width: 100%;
  padding: 18px 24px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 15px;
  font-size: 16px;
  outline: none;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  font-weight: 500;
}

.location-search:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.search-suggestions {
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 500;
}

.property-filters {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 25px;
}

.property-type-section h4 {
  margin-bottom: 18px;
  color: #333;
  font-size: 18px;
  font-weight: 700;
}

.property-types {
  display: flex;
  gap: 35px;
}

.type-category {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-title {
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  font-size: 15px;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.type-options label,
.bhk-options label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  color: #555;
  transition: color 0.2s ease;
}

.type-options label:hover,
.bhk-options label:hover {
  color: #667eea;
}

.bhk-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 18px 35px;
  border-radius: 15px;
  font-weight: 700;
  cursor: pointer;
  font-size: 16px;
  min-width: 140px;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

/* Featured Properties */
.featured-properties {
  padding: 60px 0;
  background: white;
}

.featured-properties h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.property-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
}

.property-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.property-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.property-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.property-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
  transform: scale(1.05);
}

.property-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.property-info {
  padding: 20px;
}

.price {
  font-size: 20px;
  font-weight: 700;
  color: #e03c31;
  margin-bottom: 8px;
}

.property-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.property-location {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.property-details {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 13px;
  color: #666;
}

.property-details span {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.property-actions {
  display: flex;
  gap: 10px;
}

.contact-btn {
  flex: 1;
  background: #e03c31;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

.view-btn {
  flex: 1;
  background: transparent;
  color: #e03c31;
  border: 1px solid #e03c31;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Popular Projects */
.popular-projects {
  padding: 60px 0;
  background: #f8f9fa;
}

.popular-projects h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.project-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.project-image {
  height: 180px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-info {
  padding: 20px;
}

.project-name {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
}

.project-location {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.project-price {
  font-size: 16px;
  font-weight: 600;
  color: #e03c31;
  margin-bottom: 6px;
}

.project-developer {
  color: #666;
  font-size: 13px;
  margin-bottom: 15px;
}

.view-project-btn {
  width: 100%;
  background: #e03c31;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Mumbai Highlights */
.mumbai-highlights {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.mumbai-highlights::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="waves" width="100" height="20" patternUnits="userSpaceOnUse"><path d="M0 10 Q25 0 50 10 T100 10 V20 H0 Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23waves)"/></svg>');
  opacity: 0.3;
}

.mumbai-highlights h2 {
  text-align: center;
  margin-bottom: 60px;
  font-size: 42px;
  color: white;
  font-weight: 800;
  position: relative;
  z-index: 2;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 2;
}

.highlight-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.highlight-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.highlight-card h3 {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.highlight-card p {
  color: #666;
  line-height: 1.6;
  font-size: 15px;
  font-weight: 500;
}

/* Services Section */
.services {
  padding: 60px 0;
  background: white;
}

.services h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.service-card h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 20px;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

/* Footer */
.footer {
  background-color: #2c3e50;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 20px;
  font-size: 18px;
  color: white;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #34495e;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #e03c31;
  transform: translateY(-2px);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #bdc3c7;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #e03c31;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-links {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #bdc3c7;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #e03c31;
}

.copyright {
  color: #95a5a6;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    padding: 12px 15px;
  }

  .main-nav {
    gap: 20px;
  }

  .property-grid,
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px 15px;
    height: auto;
  }

  .logo-section {
    width: 100%;
    justify-content: space-between;
  }

  .main-nav {
    display: none; /* Hide main nav on mobile, would need hamburger menu */
  }

  .header-actions {
    width: 100%;
    justify-content: center;
    gap: 8px;
  }

  .spd-prime-btn,
  .login-btn,
  .post-property-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 36px;
  }

  .hero-text p {
    font-size: 16px;
  }

  .hero-stats {
    justify-content: center;
    gap: 30px;
  }

  .hero-image img {
    height: 300px;
    transform: none;
  }

  .search-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }

  .search-tab {
    flex: 1;
    min-width: 80px;
    padding: 10px 12px;
    font-size: 14px;
  }

  .post-ad-tab {
    width: 100%;
    margin-left: 0;
    margin-top: 10px;
  }

  .property-filters {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .property-types {
    flex-direction: column;
    gap: 15px;
  }

  .property-grid,
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .logo {
    font-size: 20px;
  }

  .search-form {
    padding: 15px;
  }

  .location-search {
    padding: 12px;
    font-size: 14px;
  }

  .property-card,
  .project-card,
  .service-card {
    margin: 0 10px;
  }

  .featured-properties h2,
  .popular-projects h2,
  .services h2 {
    font-size: 24px;
  }
}

/* Hover effects and animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.property-card,
.project-card,
.service-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Button hover effects */
.search-btn:hover,
.contact-btn:hover,
.view-project-btn:hover,
.spd-prime-btn:hover,
.post-property-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(224, 60, 49, 0.3);
}

.login-btn:hover,
.view-btn:hover {
  background: #e03c31;
  color: white;
}

/* Loading states */
.property-card:hover .contact-btn,
.property-card:hover .view-btn {
  transform: scale(1.02);
}


