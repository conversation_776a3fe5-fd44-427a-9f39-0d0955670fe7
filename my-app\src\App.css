/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Smart Property Deals Styles */
.smart-property-deals {
  width: 100%;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 50px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #e03c31;
}

.nav {
  display: flex;
  gap: 25px;
}

.nav a {
  font-weight: 500;
}

.nav a:hover {
  color: #e03c31;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

.login-btn, .signup-btn {
  padding: 8px 15px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.login-btn {
  background: transparent;
  border: 1px solid #e03c31;
  color: #e03c31;
}

.signup-btn {
  background: #e03c31;
  border: 1px solid #e03c31;
  color: white;
}

/* Hero Section */
.hero {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHw%3D&w=1000&q=80');
  background-size: cover;
  background-position: center;
  height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  padding: 0 20px;
  text-align: center;
}

.hero h1 {
  font-size: 48px;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.search-container {
  width: 80%;
  max-width: 800px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.search-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.search-tabs button {
  flex: 1;
  padding: 15px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.search-tabs button.active {
  color: #e03c31;
  border-bottom: 2px solid #e03c31;
}

.search-box {
  display: flex;
  padding: 15px;
}

.search-box input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.search-btn {
  padding: 12px 25px;
  background: #e03c31;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-weight: 500;
}

/* Featured Properties */
.featured-properties {
  padding: 60px 50px;
}

.featured-properties h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
}

.property-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.property-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.property-card:hover {
  transform: translateY(-5px);
}

.property-img {
  height: 200px;
  background-color: #f5f5f5;
  background-image: url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHw%3D&w=1000&q=80');
  background-size: cover;
  background-position: center;
}

.property-details {
  padding: 20px;
}

.property-details h3 {
  color: #e03c31;
  margin-bottom: 10px;
}

.property-features {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

/* Services Section */
.services {
  padding: 60px 50px;
  background-color: #f9f9f9;
}

.services h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
}

.service-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.service-icon {
  font-size: 40px;
  margin-bottom: 20px;
}

.service-card h3 {
  margin-bottom: 15px;
  color: #333;
}

/* Footer */
.footer {
  background-color: #222;
  color: white;
  padding: 60px 50px 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 40px;
}

.footer-section h3 {
  margin-bottom: 20px;
  font-size: 18px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a:hover {
  color: #e03c31;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }
  
  .nav {
    gap: 15px;
  }
  
  .hero h1 {
    font-size: 36px;
  }
  
  .search-container {
    width: 95%;
  }
  
  .featured-properties, .services {
    padding: 40px 20px;
  }
}


