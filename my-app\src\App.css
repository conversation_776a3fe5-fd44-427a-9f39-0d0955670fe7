/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

a {
  text-decoration: none;
  color: inherit;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Smart Property Deals Styles */
.smart-property-deals {
  width: 100%;
}

/* Header */
.header {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #e03c31;
}

.city-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.main-nav {
  display: flex;
  gap: 30px;
  align-items: center;
}

.nav-item {
  position: relative;
  cursor: pointer;
  padding: 10px 0;
  font-weight: 500;
  color: #333;
}

.nav-item:hover {
  color: #e03c31;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 10px 0;
  z-index: 1001;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content a {
  display: block;
  padding: 8px 16px;
  color: #333;
  font-size: 14px;
}

.dropdown-content a:hover {
  background-color: #f5f5f5;
  color: #e03c31;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.spd-prime-btn {
  background: linear-gradient(135deg, #ff6b35, #e03c31);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

.login-btn {
  background: transparent;
  border: 1px solid #e03c31;
  color: #e03c31;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
}

.post-property-btn {
  background: #e03c31;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Hero Search Section */
.hero-search {
  background: white;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
}

.search-tab {
  flex: 1;
  padding: 12px 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: #666;
}

.search-tab.active {
  background: #e03c31;
  color: white;
}

.search-tab:hover:not(.active) {
  background: #e9ecef;
}

.post-ad-tab {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  margin-left: 10px;
}

.search-form {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.search-input-container {
  margin-bottom: 20px;
}

.location-search {
  width: 100%;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  outline: none;
}

.location-search:focus {
  border-color: #e03c31;
}

.search-suggestions {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.property-filters {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 20px;
}

.property-type-section h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.property-types {
  display: flex;
  gap: 30px;
}

.type-category {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.category-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.type-options label,
.bhk-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.bhk-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-btn {
  background: #e03c31;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  font-size: 16px;
  min-width: 120px;
}

/* Featured Properties */
.featured-properties {
  padding: 60px 0;
  background: white;
}

.featured-properties h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.property-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
}

.property-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.property-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.property-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.property-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
  transform: scale(1.05);
}

.property-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.property-info {
  padding: 20px;
}

.price {
  font-size: 20px;
  font-weight: 700;
  color: #e03c31;
  margin-bottom: 8px;
}

.property-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.property-location {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.property-details {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 13px;
  color: #666;
}

.property-details span {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.property-actions {
  display: flex;
  gap: 10px;
}

.contact-btn {
  flex: 1;
  background: #e03c31;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

.view-btn {
  flex: 1;
  background: transparent;
  color: #e03c31;
  border: 1px solid #e03c31;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Popular Projects */
.popular-projects {
  padding: 60px 0;
  background: #f8f9fa;
}

.popular-projects h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.project-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.project-image {
  height: 180px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-info {
  padding: 20px;
}

.project-name {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
}

.project-location {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.project-price {
  font-size: 16px;
  font-weight: 600;
  color: #e03c31;
  margin-bottom: 6px;
}

.project-developer {
  color: #666;
  font-size: 13px;
  margin-bottom: 15px;
}

.view-project-btn {
  width: 100%;
  background: #e03c31;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Services Section */
.services {
  padding: 60px 0;
  background: white;
}

.services h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #333;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.service-card h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 20px;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

/* Footer */
.footer {
  background-color: #2c3e50;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 20px;
  font-size: 18px;
  color: white;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #34495e;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #e03c31;
  transform: translateY(-2px);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #bdc3c7;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #e03c31;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-links {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #bdc3c7;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #e03c31;
}

.copyright {
  color: #95a5a6;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    padding: 12px 15px;
  }

  .main-nav {
    gap: 20px;
  }

  .property-grid,
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .logo-section {
    width: 100%;
    justify-content: space-between;
  }

  .main-nav {
    display: none; /* Hide main nav on mobile, would need hamburger menu */
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .search-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }

  .search-tab {
    flex: 1;
    min-width: 80px;
    padding: 10px 12px;
    font-size: 14px;
  }

  .post-ad-tab {
    width: 100%;
    margin-left: 0;
    margin-top: 10px;
  }

  .property-filters {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .property-types {
    flex-direction: column;
    gap: 15px;
  }

  .property-grid,
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .logo {
    font-size: 20px;
  }

  .search-form {
    padding: 15px;
  }

  .location-search {
    padding: 12px;
    font-size: 14px;
  }

  .property-card,
  .project-card,
  .service-card {
    margin: 0 10px;
  }

  .featured-properties h2,
  .popular-projects h2,
  .services h2 {
    font-size: 24px;
  }
}

/* Hover effects and animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.property-card,
.project-card,
.service-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Button hover effects */
.search-btn:hover,
.contact-btn:hover,
.view-project-btn:hover,
.spd-prime-btn:hover,
.post-property-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(224, 60, 49, 0.3);
}

.login-btn:hover,
.view-btn:hover {
  background: #e03c31;
  color: white;
}

/* Loading states */
.property-card:hover .contact-btn,
.property-card:hover .view-btn {
  transform: scale(1.02);
}


