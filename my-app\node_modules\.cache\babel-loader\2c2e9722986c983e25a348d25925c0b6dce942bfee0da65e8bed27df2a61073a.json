{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\REALEASTATE\\\\my-app\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [activeTab, setActiveTab] = useState('Buy');\n  const [searchQuery, setSearchQuery] = useState('');\n  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];\n\n  // Mumbai-specific data\n  const mumbaiLocalities = ['Andheri East', 'Andheri West', 'Bandra East', 'Bandra West', 'Borivali East', 'Borivali West', 'Chembur', 'Dadar East', 'Dadar West', 'Ghatkopar East', 'Ghatkopar West', 'Goregaon East', 'Goregaon West', 'Juhu', 'Kandivali East', 'Kandivali West', 'Khar West', 'Kurla East', 'Kurla West', 'Lower Parel', 'Malad East', 'Malad West', 'Matunga East', 'Matunga West', 'Mulund East', 'Mulund West', 'Powai', 'Santa Cruz East', 'Santa Cruz West', 'Thane West', 'Versova', 'Vikhroli East', 'Vikhroli West', 'Vile Parle East', 'Vile Parle West', 'Worli'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"smart-property-deals\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: \"Smart Property Deals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"city-display\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-city\",\n              children: \"Mumbai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Currently serving Mumbai only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"main-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Buy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#ready-to-move\",\n                children: \"Ready to Move\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#owner-properties\",\n                children: \"Owner Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#budget-homes\",\n                children: \"Budget Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#premium-homes\",\n                children: \"Premium Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#new-projects\",\n                children: \"New Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Rent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#owner-properties\",\n                children: \"Owner Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#verified-properties\",\n                children: \"Verified Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#furnished-homes\",\n                children: \"Furnished Homes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#bachelor-friendly\",\n                children: \"Bachelor Friendly\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Sell\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#post-property\",\n                children: \"Post Property FREE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#my-dashboard\",\n                children: \"My Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#ad-packages\",\n                children: \"Ad Packages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Home Loans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#home-loans\",\n                children: \"Home Loans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#balance-transfer\",\n                children: \"Balance Transfer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#loan-against-property\",\n                children: \"Loan Against Property\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"SPD Advice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#research-insights\",\n                children: \"Research & Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#property-valuation\",\n                children: \"Property Valuation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#rates-trends\",\n                children: \"Rates & Trends\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#area-converter\",\n                children: \"Area Converter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"spd-prime-btn\",\n            children: \"SPD Prime\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"login-btn\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"post-property-btn\",\n            children: \"Post Property FREE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Find Your Dream Home in Mumbai\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Discover the best properties in India's financial capital. From luxury apartments in South Mumbai to affordable homes in the suburbs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: \"50,000+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: \"2 Lakh+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Happy Customers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: \"500+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Verified Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.0.3&w=600&q=80\",\n            alt: \"Mumbai Skyline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-search\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-tabs\",\n          children: [searchTabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `search-tab ${activeTab === tab ? 'active' : ''}`,\n            onClick: () => setActiveTab(tab),\n            children: tab\n          }, tab, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"post-ad-tab\",\n            children: \"Post Free Property Ad\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search in Mumbai - Enter locality, area or project name\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"location-search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-suggestions\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"You can enter: Locality (like Andheri, Bandra), Area (like Western Suburbs), Project or Builder name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-type-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Property Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-types\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"type-category\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"category-title\",\n                    children: \"Residential\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"type-options\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 155,\n                        columnNumber: 30\n                      }, this), \" Flat\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 30\n                      }, this), \" House/Villa\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 157,\n                        columnNumber: 30\n                      }, this), \" Plot\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bhk-options\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 28\n                    }, this), \" 1 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 28\n                    }, this), \" 2 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 28\n                    }, this), \" 3 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 28\n                    }, this), \" 4 Bhk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-btn\",\n              children: \"Search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-properties\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Featured Properties in Mumbai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"property-grid\",\n          children: [{\n            price: '₹ 1.2 Cr',\n            type: '2 BHK Apartment',\n            location: 'Andheri West',\n            area: '850 sq.ft',\n            furnishing: 'Semi-furnished'\n          }, {\n            price: '₹ 2.5 Cr',\n            type: '3 BHK Apartment',\n            location: 'Bandra West',\n            area: '1200 sq.ft',\n            furnishing: 'Fully-furnished'\n          }, {\n            price: '₹ 95 Lac',\n            type: '2 BHK Apartment',\n            location: 'Malad West',\n            area: '750 sq.ft',\n            furnishing: 'Unfurnished'\n          }, {\n            price: '₹ 1.8 Cr',\n            type: '3 BHK Apartment',\n            location: 'Powai',\n            area: '1100 sq.ft',\n            furnishing: 'Semi-furnished'\n          }, {\n            price: '₹ 3.2 Cr',\n            type: '4 BHK Apartment',\n            location: 'Juhu',\n            area: '1800 sq.ft',\n            furnishing: 'Fully-furnished'\n          }, {\n            price: '₹ 1.5 Cr',\n            type: '3 BHK Apartment',\n            location: 'Goregaon West',\n            area: '1000 sq.ft',\n            furnishing: 'Semi-furnished'\n          }].map((property, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-image\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80\",\n                alt: \"Property\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-badge\",\n                children: \"Ready to Move\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price\",\n                children: property.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-title\",\n                children: property.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-location\",\n                children: [property.location, \", Mumbai\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: property.area\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: property.furnishing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-btn\",\n                  children: \"Contact Owner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"view-btn\",\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"popular-projects\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Popular Projects in Mumbai\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"projects-grid\",\n          children: [{\n            name: 'Lodha Park',\n            location: 'Lower Parel',\n            price: '₹ 3.5 Cr onwards',\n            developer: 'Lodha Group'\n          }, {\n            name: 'Godrej Platinum',\n            location: 'Vikhroli East',\n            price: '₹ 1.8 Cr onwards',\n            developer: 'Godrej Properties'\n          }, {\n            name: 'Oberoi Realty',\n            location: 'Goregaon East',\n            price: '₹ 2.2 Cr onwards',\n            developer: 'Oberoi Realty'\n          }, {\n            name: 'Hiranandani Gardens',\n            location: 'Powai',\n            price: '₹ 2.8 Cr onwards',\n            developer: 'Hiranandani Group'\n          }, {\n            name: 'Runwal Forests',\n            location: 'Kanjurmarg West',\n            price: '₹ 1.5 Cr onwards',\n            developer: 'Runwal Group'\n          }, {\n            name: 'Kalpataru Immensa',\n            location: 'Thane West',\n            price: '₹ 1.2 Cr onwards',\n            developer: 'Kalpataru Group'\n          }].map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80\",\n                alt: \"Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-name\",\n                children: project.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-location\",\n                children: [project.location, \", Mumbai\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-price\",\n                children: project.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-developer\",\n                children: [\"by \", project.developer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"view-project-btn\",\n                children: \"View Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"mumbai-highlights\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Why Choose Mumbai?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"highlights-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83C\\uDFD9\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Financial Capital\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Home to India's major financial institutions, stock exchanges, and corporate headquarters.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83D\\uDE87\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Excellent Connectivity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Well-connected metro, local trains, and upcoming infrastructure projects.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83C\\uDFAD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Cultural Hub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Bollywood, art galleries, theaters, and diverse cultural experiences.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83C\\uDFD6\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Coastal Living\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Beautiful beaches, sea-facing properties, and pleasant coastal climate.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83C\\uDF7D\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Food Paradise\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Street food, fine dining, and culinary diversity from across India.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"highlight-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"highlight-icon\",\n              children: \"\\uD83D\\uDCBC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Career Opportunities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Abundant job opportunities across industries and sectors.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"services\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Our Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83C\\uDFE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Buy a Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDD11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Rent a Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We're creating a seamless online experience \\u2013 from shopping on the largest rental network, to applying, to paying rent.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Home Loans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get pre-approved by a local lender to increase your chances of making a successful offer on a home.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"service-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Property Valuation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get accurate property valuations and market insights to make informed real estate decisions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Smart Property Deals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-links\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#facebook\",\n                children: \"\\uD83D\\uDCD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#twitter\",\n                children: \"\\uD83D\\uDC26\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#linkedin\",\n                children: \"\\uD83D\\uDCBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#youtube\",\n                children: \"\\uD83D\\uDCFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#instagram\",\n                children: \"\\uD83D\\uDCF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Properties in Mumbai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#andheri\",\n                  children: \"Property in Andheri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#bandra\",\n                  children: \"Property in Bandra\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#powai\",\n                  children: \"Property in Powai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#juhu\",\n                  children: \"Property in Juhu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#malad\",\n                  children: \"Property in Malad\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#goregaon\",\n                  children: \"Property in Goregaon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Mumbai Areas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#western-suburbs\",\n                  children: \"Western Suburbs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#central-mumbai\",\n                  children: \"Central Mumbai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#south-mumbai\",\n                  children: \"South Mumbai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#eastern-suburbs\",\n                  children: \"Eastern Suburbs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#thane\",\n                  children: \"Thane\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#navi-mumbai\",\n                  children: \"Navi Mumbai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Property Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#home-loans\",\n                  children: \"Home Loan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#property-valuation\",\n                  children: \"Property Valuation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#legal-services\",\n                  children: \"Legal Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#home-interiors\",\n                  children: \"Home Interiors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#packers-movers\",\n                  children: \"Packers & Movers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#terms\",\n              children: \"Terms & Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#privacy\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#sitemap\",\n              children: \"Sitemap\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#careers\",\n              children: \"Careers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#help\",\n              children: \"Help Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"copyright\",\n            children: \"\\xA9 2025 Smart Property Deals. All Rights Reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"x6FcSKa4OQMdhdDXB8LaHG+t25E=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "App", "_s", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "searchTabs", "mumbaiLocalities", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "src", "alt", "map", "tab", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "price", "location", "area", "furnishing", "property", "index", "name", "developer", "project", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/REALEASTATE/my-app/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [activeTab, setActiveTab] = useState('Buy');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n\r\n  const searchTabs = ['Buy', 'Rent', 'New Projects', 'PG', 'Plot', 'Commercial'];\r\n\r\n  // Mumbai-specific data\r\n  const mumbaiLocalities = [\r\n    'Andheri East', 'Andheri West', 'Bandra East', 'Bandra West', 'Borivali East', 'Borivali West',\r\n    'Chembur', 'Dadar East', 'Dadar West', 'Ghatkopar East', 'Ghatkopar West', 'Goregaon East',\r\n    'Goregaon West', 'Juhu', 'Kandivali East', 'Kandivali West', 'Khar West', 'Kurla East',\r\n    'Kurla West', 'Lower Parel', 'Malad East', 'Malad West', 'Matunga East', 'Matunga West',\r\n    'Mulund East', 'Mulund West', 'Powai', 'Santa Cruz East', 'Santa Cruz West', 'Thane West',\r\n    'Versova', 'Vikhroli East', 'Vikhroli West', 'Vile Parle East', 'Vile Parle West', 'Worli'\r\n  ];\r\n\r\n  return (\r\n    <div className=\"smart-property-deals\">\r\n      {/* Header */}\r\n      <header className=\"header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"logo-section\">\r\n            <div className=\"logo\">Smart Property Deals</div>\r\n            <div className=\"city-display\">\r\n              <span className=\"current-city\">Mumbai</span>\r\n              <small>Currently serving Mumbai only</small>\r\n            </div>\r\n          </div>\r\n\r\n          <nav className=\"main-nav\">\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Buy</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#ready-to-move\">Ready to Move</a>\r\n                <a href=\"#owner-properties\">Owner Properties</a>\r\n                <a href=\"#budget-homes\">Budget Homes</a>\r\n                <a href=\"#premium-homes\">Premium Homes</a>\r\n                <a href=\"#new-projects\">New Projects</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Rent</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#owner-properties\">Owner Properties</a>\r\n                <a href=\"#verified-properties\">Verified Properties</a>\r\n                <a href=\"#furnished-homes\">Furnished Homes</a>\r\n                <a href=\"#bachelor-friendly\">Bachelor Friendly</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Sell</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#post-property\">Post Property FREE</a>\r\n                <a href=\"#my-dashboard\">My Dashboard</a>\r\n                <a href=\"#ad-packages\">Ad Packages</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>Home Loans</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#home-loans\">Home Loans</a>\r\n                <a href=\"#balance-transfer\">Balance Transfer</a>\r\n                <a href=\"#loan-against-property\">Loan Against Property</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item dropdown\">\r\n              <span>SPD Advice</span>\r\n              <div className=\"dropdown-content\">\r\n                <a href=\"#research-insights\">Research & Insights</a>\r\n                <a href=\"#property-valuation\">Property Valuation</a>\r\n                <a href=\"#rates-trends\">Rates & Trends</a>\r\n                <a href=\"#area-converter\">Area Converter</a>\r\n              </div>\r\n            </div>\r\n            <div className=\"nav-item\">\r\n              <span>Help</span>\r\n            </div>\r\n          </nav>\r\n\r\n          <div className=\"header-actions\">\r\n            <button className=\"spd-prime-btn\">SPD Prime</button>\r\n            <button className=\"login-btn\">Login</button>\r\n            <button className=\"post-property-btn\">Post Property FREE</button>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Hero Banner */}\r\n      <section className=\"hero-banner\">\r\n        <div className=\"hero-content\">\r\n          <div className=\"hero-text\">\r\n            <h1>Find Your Dream Home in Mumbai</h1>\r\n            <p>Discover the best properties in India's financial capital. From luxury apartments in South Mumbai to affordable homes in the suburbs.</p>\r\n            <div className=\"hero-stats\">\r\n              <div className=\"stat\">\r\n                <span className=\"stat-number\">50,000+</span>\r\n                <span className=\"stat-label\">Properties</span>\r\n              </div>\r\n              <div className=\"stat\">\r\n                <span className=\"stat-number\">2 Lakh+</span>\r\n                <span className=\"stat-label\">Happy Customers</span>\r\n              </div>\r\n              <div className=\"stat\">\r\n                <span className=\"stat-number\">500+</span>\r\n                <span className=\"stat-label\">Verified Agents</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"hero-image\">\r\n            <img src=\"https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.0.3&w=600&q=80\" alt=\"Mumbai Skyline\" />\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Hero Search Section */}\r\n      <section className=\"hero-search\">\r\n        <div className=\"search-container\">\r\n          <div className=\"search-tabs\">\r\n            {searchTabs.map(tab => (\r\n              <button\r\n                key={tab}\r\n                className={`search-tab ${activeTab === tab ? 'active' : ''}`}\r\n                onClick={() => setActiveTab(tab)}\r\n              >\r\n                {tab}\r\n              </button>\r\n            ))}\r\n            <button className=\"post-ad-tab\">Post Free Property Ad</button>\r\n          </div>\r\n\r\n          <div className=\"search-form\">\r\n            <div className=\"search-input-container\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search in Mumbai - Enter locality, area or project name\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n                className=\"location-search\"\r\n              />\r\n              <div className=\"search-suggestions\">\r\n                <small>You can enter: Locality (like Andheri, Bandra), Area (like Western Suburbs), Project or Builder name</small>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"property-filters\">\r\n              <div className=\"property-type-section\">\r\n                <h4>Property Type</h4>\r\n                <div className=\"property-types\">\r\n                  <div className=\"type-category\">\r\n                    <span className=\"category-title\">Residential</span>\r\n                    <div className=\"type-options\">\r\n                      <label><input type=\"checkbox\" /> Flat</label>\r\n                      <label><input type=\"checkbox\" /> House/Villa</label>\r\n                      <label><input type=\"checkbox\" /> Plot</label>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"bhk-options\">\r\n                    <label><input type=\"checkbox\" /> 1 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 2 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 3 Bhk</label>\r\n                    <label><input type=\"checkbox\" /> 4 Bhk</label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <button className=\"search-btn\">Search</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Featured Properties */}\r\n      <section className=\"featured-properties\">\r\n        <div className=\"container\">\r\n          <h2>Featured Properties in Mumbai</h2>\r\n          <div className=\"property-grid\">\r\n            {[\r\n              { price: '₹ 1.2 Cr', type: '2 BHK Apartment', location: 'Andheri West', area: '850 sq.ft', furnishing: 'Semi-furnished' },\r\n              { price: '₹ 2.5 Cr', type: '3 BHK Apartment', location: 'Bandra West', area: '1200 sq.ft', furnishing: 'Fully-furnished' },\r\n              { price: '₹ 95 Lac', type: '2 BHK Apartment', location: 'Malad West', area: '750 sq.ft', furnishing: 'Unfurnished' },\r\n              { price: '₹ 1.8 Cr', type: '3 BHK Apartment', location: 'Powai', area: '1100 sq.ft', furnishing: 'Semi-furnished' },\r\n              { price: '₹ 3.2 Cr', type: '4 BHK Apartment', location: 'Juhu', area: '1800 sq.ft', furnishing: 'Fully-furnished' },\r\n              { price: '₹ 1.5 Cr', type: '3 BHK Apartment', location: 'Goregaon West', area: '1000 sq.ft', furnishing: 'Semi-furnished' }\r\n            ].map((property, index) => (\r\n              <div className=\"property-card\" key={index}>\r\n                <div className=\"property-image\">\r\n                  <img src=\"https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&w=300&q=80\" alt=\"Property\" />\r\n                  <div className=\"property-badge\">Ready to Move</div>\r\n                </div>\r\n                <div className=\"property-info\">\r\n                  <div className=\"price\">{property.price}</div>\r\n                  <div className=\"property-title\">{property.type}</div>\r\n                  <div className=\"property-location\">{property.location}, Mumbai</div>\r\n                  <div className=\"property-details\">\r\n                    <span>{property.area}</span>\r\n                    <span>{property.furnishing}</span>\r\n                  </div>\r\n                  <div className=\"property-actions\">\r\n                    <button className=\"contact-btn\">Contact Owner</button>\r\n                    <button className=\"view-btn\">View Details</button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Popular Projects */}\r\n      <section className=\"popular-projects\">\r\n        <div className=\"container\">\r\n          <h2>Popular Projects in Mumbai</h2>\r\n          <div className=\"projects-grid\">\r\n            {[\r\n              { name: 'Lodha Park', location: 'Lower Parel', price: '₹ 3.5 Cr onwards', developer: 'Lodha Group' },\r\n              { name: 'Godrej Platinum', location: 'Vikhroli East', price: '₹ 1.8 Cr onwards', developer: 'Godrej Properties' },\r\n              { name: 'Oberoi Realty', location: 'Goregaon East', price: '₹ 2.2 Cr onwards', developer: 'Oberoi Realty' },\r\n              { name: 'Hiranandani Gardens', location: 'Powai', price: '₹ 2.8 Cr onwards', developer: 'Hiranandani Group' },\r\n              { name: 'Runwal Forests', location: 'Kanjurmarg West', price: '₹ 1.5 Cr onwards', developer: 'Runwal Group' },\r\n              { name: 'Kalpataru Immensa', location: 'Thane West', price: '₹ 1.2 Cr onwards', developer: 'Kalpataru Group' }\r\n            ].map((project, index) => (\r\n              <div className=\"project-card\" key={index}>\r\n                <div className=\"project-image\">\r\n                  <img src=\"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&w=300&q=80\" alt=\"Project\" />\r\n                </div>\r\n                <div className=\"project-info\">\r\n                  <div className=\"project-name\">{project.name}</div>\r\n                  <div className=\"project-location\">{project.location}, Mumbai</div>\r\n                  <div className=\"project-price\">{project.price}</div>\r\n                  <div className=\"project-developer\">by {project.developer}</div>\r\n                  <button className=\"view-project-btn\">View Project</button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Mumbai Highlights */}\r\n      <section className=\"mumbai-highlights\">\r\n        <div className=\"container\">\r\n          <h2>Why Choose Mumbai?</h2>\r\n          <div className=\"highlights-grid\">\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">🏙️</div>\r\n              <h3>Financial Capital</h3>\r\n              <p>Home to India's major financial institutions, stock exchanges, and corporate headquarters.</p>\r\n            </div>\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">🚇</div>\r\n              <h3>Excellent Connectivity</h3>\r\n              <p>Well-connected metro, local trains, and upcoming infrastructure projects.</p>\r\n            </div>\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">🎭</div>\r\n              <h3>Cultural Hub</h3>\r\n              <p>Bollywood, art galleries, theaters, and diverse cultural experiences.</p>\r\n            </div>\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">🏖️</div>\r\n              <h3>Coastal Living</h3>\r\n              <p>Beautiful beaches, sea-facing properties, and pleasant coastal climate.</p>\r\n            </div>\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">🍽️</div>\r\n              <h3>Food Paradise</h3>\r\n              <p>Street food, fine dining, and culinary diversity from across India.</p>\r\n            </div>\r\n            <div className=\"highlight-card\">\r\n              <div className=\"highlight-icon\">💼</div>\r\n              <h3>Career Opportunities</h3>\r\n              <p>Abundant job opportunities across industries and sectors.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Services Section */}\r\n      <section className=\"services\">\r\n        <div className=\"container\">\r\n          <h2>Our Services</h2>\r\n          <div className=\"services-grid\">\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">🏠</div>\r\n              <h3>Buy a Home</h3>\r\n              <p>Find your place with an immersive photo experience and the most listings, including things you won't find anywhere else.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">🔑</div>\r\n              <h3>Rent a Home</h3>\r\n              <p>We're creating a seamless online experience – from shopping on the largest rental network, to applying, to paying rent.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">💰</div>\r\n              <h3>Home Loans</h3>\r\n              <p>Get pre-approved by a local lender to increase your chances of making a successful offer on a home.</p>\r\n            </div>\r\n            <div className=\"service-card\">\r\n              <div className=\"service-icon\">📊</div>\r\n              <h3>Property Valuation</h3>\r\n              <p>Get accurate property valuations and market insights to make informed real estate decisions.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"container\">\r\n          <div className=\"footer-content\">\r\n            <div className=\"footer-section\">\r\n              <h3>Smart Property Deals</h3>\r\n              <p>As the largest platform connecting property buyers and sellers, Smart Property Deals boasts over 2 crore monthly visitors and 15 lakh active property listings.</p>\r\n              <div className=\"social-links\">\r\n                <a href=\"#facebook\">📘</a>\r\n                <a href=\"#twitter\">🐦</a>\r\n                <a href=\"#linkedin\">💼</a>\r\n                <a href=\"#youtube\">📺</a>\r\n                <a href=\"#instagram\">📷</a>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>Properties in Mumbai</h4>\r\n              <ul>\r\n                <li><a href=\"#andheri\">Property in Andheri</a></li>\r\n                <li><a href=\"#bandra\">Property in Bandra</a></li>\r\n                <li><a href=\"#powai\">Property in Powai</a></li>\r\n                <li><a href=\"#juhu\">Property in Juhu</a></li>\r\n                <li><a href=\"#malad\">Property in Malad</a></li>\r\n                <li><a href=\"#goregaon\">Property in Goregaon</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>Mumbai Areas</h4>\r\n              <ul>\r\n                <li><a href=\"#western-suburbs\">Western Suburbs</a></li>\r\n                <li><a href=\"#central-mumbai\">Central Mumbai</a></li>\r\n                <li><a href=\"#south-mumbai\">South Mumbai</a></li>\r\n                <li><a href=\"#eastern-suburbs\">Eastern Suburbs</a></li>\r\n                <li><a href=\"#thane\">Thane</a></li>\r\n                <li><a href=\"#navi-mumbai\">Navi Mumbai</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"footer-section\">\r\n              <h4>Property Services</h4>\r\n              <ul>\r\n                <li><a href=\"#home-loans\">Home Loan</a></li>\r\n                <li><a href=\"#property-valuation\">Property Valuation</a></li>\r\n                <li><a href=\"#legal-services\">Legal Services</a></li>\r\n                <li><a href=\"#home-interiors\">Home Interiors</a></li>\r\n                <li><a href=\"#packers-movers\">Packers & Movers</a></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"footer-bottom\">\r\n            <div className=\"footer-links\">\r\n              <a href=\"#terms\">Terms & Conditions</a>\r\n              <a href=\"#privacy\">Privacy Policy</a>\r\n              <a href=\"#sitemap\">Sitemap</a>\r\n              <a href=\"#careers\">Careers</a>\r\n              <a href=\"#help\">Help Center</a>\r\n            </div>\r\n            <p className=\"copyright\">© 2025 Smart Property Deals. All Rights Reserved.</p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMS,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,CAAC;;EAE9E;EACA,MAAMC,gBAAgB,GAAG,CACvB,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAC9F,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,EAC1F,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EACtF,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EACvF,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EACzF,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,CAC3F;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBAEnCV,OAAA;MAAQS,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBV,OAAA;QAAKS,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BV,OAAA;UAAKS,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BV,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAMS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5Cd,OAAA;cAAAU,QAAA,EAAO;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBV,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxCd,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,sBAAsB;gBAAAL,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtDd,OAAA;gBAAGe,IAAI,EAAC,kBAAkB;gBAAAL,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9Cd,OAAA;gBAAGe,IAAI,EAAC,oBAAoB;gBAAAL,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,gBAAgB;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/Cd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxCd,OAAA;gBAAGe,IAAI,EAAC,cAAc;gBAAAL,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,aAAa;gBAAAL,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpCd,OAAA;gBAAGe,IAAI,EAAC,mBAAmB;gBAAAL,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDd,OAAA;gBAAGe,IAAI,EAAC,wBAAwB;gBAAAL,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAAU,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBd,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAGe,IAAI,EAAC,oBAAoB;gBAAAL,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDd,OAAA;gBAAGe,IAAI,EAAC,qBAAqB;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDd,OAAA;gBAAGe,IAAI,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Cd,OAAA;gBAAGe,IAAI,EAAC,iBAAiB;gBAAAL,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBV,OAAA;cAAAU,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BV,OAAA;YAAQS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDd,OAAA;YAAQS,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5Cd,OAAA;YAAQS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTd,OAAA;MAASS,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9BV,OAAA;QAAKS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BV,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBV,OAAA;YAAAU,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvCd,OAAA;YAAAU,QAAA,EAAG;UAAqI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5Id,OAAA;YAAKS,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBV,OAAA;cAAKS,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBV,OAAA;gBAAMS,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5Cd,OAAA;gBAAMS,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBV,OAAA;gBAAMS,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5Cd,OAAA;gBAAMS,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBV,OAAA;gBAAMS,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCd,OAAA;gBAAMS,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNd,OAAA;UAAKS,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBV,OAAA;YAAKgB,GAAG,EAAC,wFAAwF;YAACC,GAAG,EAAC;UAAgB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9BV,OAAA;QAAKS,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BV,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBH,UAAU,CAACW,GAAG,CAACC,GAAG,iBACjBnB,OAAA;YAEES,SAAS,EAAE,cAAcN,SAAS,KAAKgB,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC7DC,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAACe,GAAG,CAAE;YAAAT,QAAA,EAEhCS;UAAG,GAJCA,GAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKF,CACT,CAAC,eACFd,OAAA;YAAQS,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BV,OAAA;YAAKS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCV,OAAA;cACEqB,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yDAAyD;cACrEC,KAAK,EAAElB,WAAY;cACnBmB,QAAQ,EAAGC,CAAC,IAAKnB,cAAc,CAACmB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDd,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFd,OAAA;cAAKS,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACjCV,OAAA;gBAAAU,QAAA,EAAO;cAAoG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BV,OAAA;cAAKS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCV,OAAA;gBAAAU,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BV,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BV,OAAA;oBAAMS,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDd,OAAA;oBAAKS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BV,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOqB,IAAI,EAAC;sBAAU;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAAK;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7Cd,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOqB,IAAI,EAAC;sBAAU;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAY;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpDd,OAAA;sBAAAU,QAAA,gBAAOV,OAAA;wBAAOqB,IAAI,EAAC;sBAAU;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,SAAK;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BV,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOqB,IAAI,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOqB,IAAI,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOqB,IAAI,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9Cd,OAAA;oBAAAU,QAAA,gBAAOV,OAAA;sBAAOqB,IAAI,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,UAAM;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENd,OAAA;cAAQS,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B,CACC;YAAEiB,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,cAAc;YAAEC,IAAI,EAAE,WAAW;YAAEC,UAAU,EAAE;UAAiB,CAAC,EACzH;YAAEH,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,aAAa;YAAEC,IAAI,EAAE,YAAY;YAAEC,UAAU,EAAE;UAAkB,CAAC,EAC1H;YAAEH,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,YAAY;YAAEC,IAAI,EAAE,WAAW;YAAEC,UAAU,EAAE;UAAc,CAAC,EACpH;YAAEH,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,OAAO;YAAEC,IAAI,EAAE,YAAY;YAAEC,UAAU,EAAE;UAAiB,CAAC,EACnH;YAAEH,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,MAAM;YAAEC,IAAI,EAAE,YAAY;YAAEC,UAAU,EAAE;UAAkB,CAAC,EACnH;YAAEH,KAAK,EAAE,UAAU;YAAEN,IAAI,EAAE,iBAAiB;YAAEO,QAAQ,EAAE,eAAe;YAAEC,IAAI,EAAE,YAAY;YAAEC,UAAU,EAAE;UAAiB,CAAC,CAC5H,CAACZ,GAAG,CAAC,CAACa,QAAQ,EAAEC,KAAK,kBACpBhC,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BV,OAAA;gBAAKgB,GAAG,EAAC,qFAAqF;gBAACC,GAAG,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChHd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BV,OAAA;gBAAKS,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAEqB,QAAQ,CAACJ;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7Cd,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEqB,QAAQ,CAACV;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDd,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAEqB,QAAQ,CAACH,QAAQ,EAAC,UAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpEd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BV,OAAA;kBAAAU,QAAA,EAAOqB,QAAQ,CAACF;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5Bd,OAAA;kBAAAU,QAAA,EAAOqB,QAAQ,CAACD;gBAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BV,OAAA;kBAAQS,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDd,OAAA;kBAAQS,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAjB4BkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBpC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B,CACC;YAAEuB,IAAI,EAAE,YAAY;YAAEL,QAAQ,EAAE,aAAa;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAc,CAAC,EACpG;YAAED,IAAI,EAAE,iBAAiB;YAAEL,QAAQ,EAAE,eAAe;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAoB,CAAC,EACjH;YAAED,IAAI,EAAE,eAAe;YAAEL,QAAQ,EAAE,eAAe;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAgB,CAAC,EAC3G;YAAED,IAAI,EAAE,qBAAqB;YAAEL,QAAQ,EAAE,OAAO;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAoB,CAAC,EAC7G;YAAED,IAAI,EAAE,gBAAgB;YAAEL,QAAQ,EAAE,iBAAiB;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAe,CAAC,EAC7G;YAAED,IAAI,EAAE,mBAAmB;YAAEL,QAAQ,EAAE,YAAY;YAAED,KAAK,EAAE,kBAAkB;YAAEO,SAAS,EAAE;UAAkB,CAAC,CAC/G,CAAChB,GAAG,CAAC,CAACiB,OAAO,EAAEH,KAAK,kBACnBhC,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BV,OAAA;gBAAKgB,GAAG,EAAC,qFAAqF;gBAACC,GAAG,EAAC;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAKS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEyB,OAAO,CAACF;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDd,OAAA;gBAAKS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAEyB,OAAO,CAACP,QAAQ,EAAC,UAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEd,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEyB,OAAO,CAACR;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDd,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,KAAG,EAACyB,OAAO,CAACD,SAAS;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/Dd,OAAA;gBAAQS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA,GAV2BkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWnC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACpCV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3Bd,OAAA;UAAKS,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BV,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzCd,OAAA;cAAAU,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Bd,OAAA;cAAAU,QAAA,EAAG;YAA0F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxCd,OAAA;cAAAU,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/Bd,OAAA;cAAAU,QAAA,EAAG;YAAyE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxCd,OAAA;cAAAU,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBd,OAAA;cAAAU,QAAA,EAAG;YAAqE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzCd,OAAA;cAAAU,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBd,OAAA;cAAAU,QAAA,EAAG;YAAuE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzCd,OAAA;cAAAU,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBd,OAAA;cAAAU,QAAA,EAAG;YAAmE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAKS,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxCd,OAAA;cAAAU,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7Bd,OAAA;cAAAU,QAAA,EAAG;YAAyD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,UAAU;MAAAC,QAAA,eAC3BV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBd,OAAA;cAAAU,QAAA,EAAG;YAAwH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBd,OAAA;cAAAU,QAAA,EAAG;YAAuH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBd,OAAA;cAAAU,QAAA,EAAG;YAAmG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCd,OAAA;cAAAU,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3Bd,OAAA;cAAAU,QAAA,EAAG;YAA4F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAAQS,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAKS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BV,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7Bd,OAAA;cAAAU,QAAA,EAAG;YAA+J;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtKd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAGe,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Bd,OAAA;gBAAGe,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzBd,OAAA;gBAAGe,IAAI,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1Bd,OAAA;gBAAGe,IAAI,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzBd,OAAA;gBAAGe,IAAI,EAAC,YAAY;gBAAAL,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7Bd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,UAAU;kBAAAL,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,QAAQ;kBAAAL,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,QAAQ;kBAAAL,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,WAAW;kBAAAL,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,kBAAkB;kBAAAL,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,eAAe;kBAAAL,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,kBAAkB;kBAAAL,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,QAAQ;kBAAAL,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,cAAc;kBAAAL,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cAAAU,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Bd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,aAAa;kBAAAL,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5Cd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7Dd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDd,OAAA;gBAAAU,QAAA,eAAIV,OAAA;kBAAGe,IAAI,EAAC,iBAAiB;kBAAAL,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAGe,IAAI,EAAC,QAAQ;cAAAL,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvCd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Bd,OAAA;cAAGe,IAAI,EAAC,UAAU;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Bd,OAAA;cAAGe,IAAI,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNd,OAAA;YAAGS,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACZ,EAAA,CApXQD,GAAG;AAAAmC,EAAA,GAAHnC,GAAG;AAsXZ,eAAeA,GAAG;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}